// core/services/form-state-manager.service.ts

import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { AbstractControl, FormGroup } from '@angular/forms';

export interface FormState {
  values: any;
  errors: any;
  touched: any;
  dirty: any;
  disabled: any;
  valid: boolean;
  pristine: boolean;
  submitted: boolean;
  timestamp: number;
}

export interface StateSnapshot {
  id: string;
  state: FormState;
  timestamp: number;
  description?: string;
}

@Injectable({
  providedIn: 'root',
})
export class FormStateManager {
  private stateHistory: StateSnapshot[] = [];
  private currentStateIndex = -1;
  private maxHistorySize = 50;
  private stateSubject = new BehaviorSubject<FormState | null>(null);

  public state$ = this.stateSubject.asObservable();

  public captureState(
    form: AbstractControl,
    description?: string
  ): StateSnapshot {
    const state = this.extractState(form);
    const snapshot: StateSnapshot = {
      id: this.generateId(),
      state,
      timestamp: Date.now(),
      description,
    };

    if (this.currentStateIndex < this.stateHistory.length - 1) {
      this.stateHistory = this.stateHistory.slice(
        0,
        this.currentStateIndex + 1
      );
    }

    this.stateHistory.push(snapshot);

    if (this.stateHistory.length > this.maxHistorySize) {
      this.stateHistory.shift();
    } else {
      this.currentStateIndex++;
    }

    this.stateSubject.next(state);

    return snapshot;
  }

  private extractState(control: AbstractControl): FormState {
    return {
      values: control.value,
      errors: this.extractErrors(control),
      touched: this.extractTouchedState(control),
      dirty: this.extractDirtyState(control),
      disabled: this.extractDisabledState(control),
      valid: control.valid,
      pristine: control.pristine,
      submitted: false,
      timestamp: Date.now(),
    };
  }

  private extractErrors(control: AbstractControl, path = ''): any {
    const errors: any = {};

    if (control.errors) {
      errors[path || 'root'] = control.errors;
    }

    if (control instanceof FormGroup) {
      Object.keys(control.controls).forEach((key) => {
        const childPath = path ? `${path}.${key}` : key;
        Object.assign(
          errors,
          this.extractErrors(control.controls[key], childPath)
        );
      });
    }

    return errors;
  }

  private extractTouchedState(control: AbstractControl, path = ''): any {
    const state: any = {};
    state[path || 'root'] = control.touched;

    if (control instanceof FormGroup) {
      Object.keys(control.controls).forEach((key) => {
        const childPath = path ? `${path}.${key}` : key;
        Object.assign(
          state,
          this.extractTouchedState(control.controls[key], childPath)
        );
      });
    }

    return state;
  }

  private extractDirtyState(control: AbstractControl, path = ''): any {
    const state: any = {};
    state[path || 'root'] = control.dirty;

    if (control instanceof FormGroup) {
      Object.keys(control.controls).forEach((key) => {
        const childPath = path ? `${path}.${key}` : key;
        Object.assign(
          state,
          this.extractDirtyState(control.controls[key], childPath)
        );
      });
    }

    return state;
  }

  private extractDisabledState(control: AbstractControl, path = ''): any {
    const state: any = {};
    state[path || 'root'] = control.disabled;

    if (control instanceof FormGroup) {
      Object.keys(control.controls).forEach((key) => {
        const childPath = path ? `${path}.${key}` : key;
        Object.assign(
          state,
          this.extractDisabledState(control.controls[key], childPath)
        );
      });
    }

    return state;
  }

  public restoreState(form: AbstractControl, snapshot: StateSnapshot): void {
    form.patchValue(snapshot.state.values);
    this.restoreTouchedState(form, snapshot.state.touched);
    this.restoreDirtyState(form, snapshot.state.dirty);
    this.stateSubject.next(snapshot.state);
  }

  private restoreTouchedState(
    control: AbstractControl,
    state: any,
    path = ''
  ): void {
    const key = path || 'root';

    if (state[key]) {
      control.markAsTouched();
    } else {
      control.markAsUntouched();
    }

    if (control instanceof FormGroup) {
      Object.keys(control.controls).forEach((controlKey) => {
        const childPath = path ? `${path}.${controlKey}` : controlKey;
        this.restoreTouchedState(
          control.controls[controlKey],
          state,
          childPath
        );
      });
    }
  }

  private restoreDirtyState(
    control: AbstractControl,
    state: any,
    path = ''
  ): void {
    const key = path || 'root';

    if (state[key]) {
      control.markAsDirty();
    } else {
      control.markAsPristine();
    }

    if (control instanceof FormGroup) {
      Object.keys(control.controls).forEach((controlKey) => {
        const childPath = path ? `${path}.${controlKey}` : controlKey;
        this.restoreDirtyState(control.controls[controlKey], state, childPath);
      });
    }
  }

  public undo(form: AbstractControl): boolean {
    if (!this.canUndo()) return false;

    this.currentStateIndex--;
    const snapshot = this.stateHistory[this.currentStateIndex];
    this.restoreState(form, snapshot);

    return true;
  }

  public redo(form: AbstractControl): boolean {
    if (!this.canRedo()) return false;

    this.currentStateIndex++;
    const snapshot = this.stateHistory[this.currentStateIndex];
    this.restoreState(form, snapshot);

    return true;
  }

  public canUndo(): boolean {
    return this.currentStateIndex > 0;
  }

  public canRedo(): boolean {
    return this.currentStateIndex < this.stateHistory.length - 1;
  }

  public getHistory(): StateSnapshot[] {
    return [...this.stateHistory];
  }

  public clearHistory(): void {
    this.stateHistory = [];
    this.currentStateIndex = -1;
    this.stateSubject.next(null);
  }

  public getCurrentState(): FormState | null {
    return this.stateSubject.value;
  }

  public getSnapshot(id: string): StateSnapshot | undefined {
    return this.stateHistory.find((s) => s.id === id);
  }

  public setMaxHistorySize(size: number): void {
    this.maxHistorySize = size;

    if (this.stateHistory.length > size) {
      this.stateHistory = this.stateHistory.slice(-size);
      this.currentStateIndex = this.stateHistory.length - 1;
    }
  }

  private generateId(): string {
    return `snapshot_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  public saveToStorage(key: string, snapshot: StateSnapshot): void {
    try {
      localStorage.setItem(key, JSON.stringify(snapshot));
    } catch (e) {
      console.error('Failed to save state to storage', e);
    }
  }

  public loadFromStorage(key: string): StateSnapshot | null {
    try {
      const data = localStorage.getItem(key);
      return data ? JSON.parse(data) : null;
    } catch (e) {
      console.error('Failed to load state from storage', e);
      return null;
    }
  }

  public removeFromStorage(key: string): void {
    try {
      localStorage.removeItem(key);
    } catch (e) {
      console.error('Failed to remove state from storage', e);
    }
  }
}
